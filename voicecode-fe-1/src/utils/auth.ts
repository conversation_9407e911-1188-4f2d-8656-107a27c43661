/**
 * Authentication utilities for token management
 */

/**
 * Get the current auth token from localStorage
 * @returns JWT token string or null if not found
 */
export function getAuthToken(): string | null {
  try {
    const authData = localStorage.getItem('voicecode-auth-store')
    if (authData) {
      const parsed = JSON.parse(authData)
      const token = parsed.state?.token
      if (token) {
        console.log('🔐 Retrieved auth token from localStorage')
        return token
      } else {
        console.warn('⚠️ No token found in auth store')
      }
    } else {
      console.warn('⚠️ No auth data found in localStorage')
    }
  } catch (error) {
    console.error('Failed to parse auth data from localStorage:', error)
  }
  
  return null
}

/**
 * Check if auth token is available
 * @returns boolean indicating if token exists
 */
export function hasAuthToken(): boolean {
  return getAuthToken() !== null
}

/**
 * Clear auth token from localStorage
 */
export function clearAuthToken(): void {
  try {
    localStorage.removeItem('voicecode-auth-store')
    console.log('🔓 Cleared auth token from localStorage')
  } catch (error) {
    console.error('Failed to clear auth token:', error)
  }
}