/* eslint-disable @typescript-eslint/no-explicit-any */
// Native HTTP service requires any types for API compatibility

import { CapacitorHttp } from '@capacitor/core'
import { Capacitor } from '@capacitor/core'

interface HttpOptions {
  url: string
  method?: 'GET' | 'POST' | 'PUT' | 'DELETE'
  headers?: Record<string, string>
  data?: any
  timeout?: number
}

interface HttpResponse<T = any> {
  data: T
  status: number
  headers: Record<string, string>
}

export class NativeHttpClient {
  private baseURL: string

  constructor(baseURL: string) {
    this.baseURL = baseURL
  }

  private async makeRequest<T>(options: HttpOptions): Promise<HttpResponse<T>> {
    const fullUrl = `${this.baseURL}${options.url}`
    
    // Default headers
    const headers: Record<string, string> = {
      'Content-Type': 'application/json',
      'Accept': 'application/json, text/plain, */*',
      ...options.headers
    }

    console.log('🌐 NativeHttp: Making request:', {
      platform: Capacitor.getPlatform(),
      method: options.method || 'GET',
      url: fullUrl,
      headers: JSON.stringify(headers, null, 2),
      data: options.data
    })

    try {
      if (Capacitor.isNativePlatform()) {
        // Use Capacitor HTTP for native platforms (iOS/Android)
        console.log('🌐 Using Capacitor HTTP for native platform')
        
        const response = await CapacitorHttp.request({
          url: fullUrl,
          method: options.method || 'GET',
          headers,
          data: options.data
        })

        console.log('🌐✅ Capacitor HTTP response:', {
          status: response.status,
          headers: JSON.stringify(response.headers, null, 2),
          data: JSON.stringify(response.data, null, 2)
        })

        return {
          data: response.data,
          status: response.status,
          headers: response.headers
        }
      } else {
        // Use fetch for web platform
        console.log('🌐 Using fetch for web platform')
        
        const fetchOptions: RequestInit = {
          method: options.method || 'GET',
          headers,
          body: options.data ? JSON.stringify(options.data) : undefined
        }

        const response = await fetch(fullUrl, fetchOptions)
        const data = await response.json()

        console.log('🌐✅ Fetch response:', {
          status: response.status,
          headers: JSON.stringify(Object.fromEntries(response.headers.entries()), null, 2),
          data: JSON.stringify(data, null, 2)
        })

        return {
          data,
          status: response.status,
          headers: Object.fromEntries(response.headers.entries())
        }
      }
    } catch (error) {
      console.error('🌐❌ NativeHttp error:', error)
      console.error('🌐❌ Error details:', JSON.stringify(error, Object.getOwnPropertyNames(error), 2))
      throw error
    }
  }

  async get<T>(url: string, headers?: Record<string, string>): Promise<HttpResponse<T>> {
    return this.makeRequest<T>({ url, method: 'GET', headers })
  }

  async post<T>(url: string, data?: any, headers?: Record<string, string>): Promise<HttpResponse<T>> {
    return this.makeRequest<T>({ url, method: 'POST', data, headers })
  }

  async put<T>(url: string, data?: any, headers?: Record<string, string>): Promise<HttpResponse<T>> {
    return this.makeRequest<T>({ url, method: 'PUT', data, headers })
  }

  async delete<T>(url: string, headers?: Record<string, string>): Promise<HttpResponse<T>> {
    return this.makeRequest<T>({ url, method: 'DELETE', headers })
  }
}

// Export singleton instance
const baseURL = import.meta.env.VITE_API_BASE_URL || 'http://localhost:9100'
export const nativeHttpClient = new NativeHttpClient(baseURL)