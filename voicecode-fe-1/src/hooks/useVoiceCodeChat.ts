import { useChat } from 'ai/react'
import { useState, useEffect, useCallback, useRef } from 'react'
import type { Message } from 'ai'
import { FastAPIAdapter } from '@/lib/fastapi-adapter'
import type { TaskInfo, TaskError, StreamingState, StreamingConnectionState } from '@/types/task.types'
import { isStreamingSupported, StreamingConnectionManager, DEFAULT_STREAMING_OPTIONS, buildStreamingUrl } from '@/utils/streaming'
import { getAuthToken } from '@/utils/auth'

interface UseVoiceCodeChatOptions {
  initialMessages?: Message[]
  onCommandExecute?: (command: string, sandboxId: string) => void
  onError?: (error: Error) => void
  // New streaming options
  enableStreaming?: boolean
  onTaskStart?: (task: TaskInfo) => void
  onTaskComplete?: (task: TaskInfo) => void
  onTaskError?: (error: TaskError) => void
}

interface UseVoiceCodeChatReturn {
  messages: Message[]
  input: string
  setInput: (value: string) => void
  handleSubmit: (e?: React.FormEvent) => void
  isLoading: boolean
  isLoadingHistory: boolean
  isSendingMessage: boolean
  error: string | null
  reload: () => void
  clearHistory: () => Promise<void>
  executeCommand: (command: string) => Promise<void>
  pagination: {
    page: number
    total: number
    hasNextPage: boolean
  }
  loadMoreHistory: () => Promise<void>
  // New streaming returns
  activeTask: TaskInfo | null
  cancelTask: () => void
  isStreaming: boolean
  streamingState: StreamingState
  // Message threading utilities
  getTaskMessages: (taskId: string) => Message[]
  getActiveThreads: () => Map<string, Message[]>
  // Enhanced task management
  taskHistory: Map<string, TaskInfo>
  taskMetrics: {
    totalTasks: number
    completedTasks: number
    failedTasks: number
    averageExecutionTime: number
  }
  getTaskById: (taskId: string) => TaskInfo | undefined
  getTasksByStatus: (status: TaskInfo['status']) => TaskInfo[]
  getRecentTasks: (limit?: number) => TaskInfo[]
  clearTaskHistory: () => void
  // Connection management
  connectionMetrics: {
    totalConnections: number
    failedConnections: number
    reconnectAttempts: number
    lastConnectionTime: Date | null
    averageConnectionTime: number
  }
  reconnectStreaming: () => Promise<void>
  getConnectionHealth: () => {
    isHealthy: boolean
    state: StreamingConnectionState
    reconnectAttempts: number
    canReconnect: boolean
  }
  resetConnectionMetrics: () => void
}

/**
 * Custom hook that bridges Vercel AI SDK with VoiceCode's FastAPI backend
 */
export function useVoiceCodeChat(
  sandboxId: string,
  options: UseVoiceCodeChatOptions = {}
): UseVoiceCodeChatReturn {
  const [adapter] = useState(() => new FastAPIAdapter())
  const [isLoadingHistory, setIsLoadingHistory] = useState(false)
  const [isExecutingCommand, setIsExecutingCommand] = useState(false)
  const [error, setError] = useState<string | null>(null)
  const [pagination, setPagination] = useState({
    page: 1,
    total: 0,
    hasNextPage: false
  })
  const [historyLoaded, setHistoryLoaded] = useState(false)
  const loadingRef = useRef(false)

  // New streaming state
  const [streamingState, setStreamingState] = useState<StreamingState>({
    isStreaming: false,
    connectionState: 'disconnected',
    activeTask: null,
    reconnectAttempts: 0,
    lastError: undefined
  })

  // Streaming control refs
  const streamingControlRef = useRef<{
    cancel: () => void
  } | null>(null)
  const connectionManagerRef = useRef<StreamingConnectionManager | null>(null)

  // Connection management state
  const [connectionMetrics, setConnectionMetrics] = useState({
    totalConnections: 0,
    failedConnections: 0,
    reconnectAttempts: 0,
    lastConnectionTime: null as Date | null,
    averageConnectionTime: 0
  })

  // Check if streaming is enabled and supported
  const isStreamingEnabled = options.enableStreaming !== false && isStreamingSupported()
  
  // Debug logging for streaming status
  useEffect(() => {
    console.log('🌊 Streaming Debug:', {
      enableStreamingOption: options.enableStreaming,
      isStreamingSupported: isStreamingSupported(), 
      isStreamingEnabled,
      sandboxId
    })
  }, [options.enableStreaming, isStreamingEnabled, sandboxId])

  // Message threading state for streaming
  const messageThreadRef = useRef<Map<string, Message[]>>(new Map())
  const messageSequenceRef = useRef<number>(0)

  // Enhanced task state management
  const [taskHistory, setTaskHistory] = useState<Map<string, TaskInfo>>(new Map())
  const [taskMetrics, setTaskMetrics] = useState({
    totalTasks: 0,
    completedTasks: 0,
    failedTasks: 0,
    averageExecutionTime: 0
  })
  const taskTimeoutRef = useRef<Map<string, NodeJS.Timeout>>(new Map())
  const taskStartTimesRef = useRef<Map<string, number>>(new Map())

  /**
   * Filter out system/meta messages and keep only actual command output
   */
  const isActualCommandOutput = useCallback((content: string): boolean => {
    // Filter out common system message patterns
    const systemMessagePatterns = [
      'Executing:',
      'Starting command execution:',
      'Command completed',
      'Task completed',
      'Running command:',
      'Command started:',
      'Command finished:'
    ]
    
    return !systemMessagePatterns.some(pattern => content.startsWith(pattern))
  }, [])



  /**
   * Process streaming message with proper ordering and threading
   */
  const processStreamingMessage = useCallback(async (message: Message, command: string) => {
    // Cast message to include metadata - temporary fix for build
    const messageWithMetadata = message as Message & { metadata?: Record<string, unknown> }
    const taskId = messageWithMetadata.metadata?.task_id as string
    const messageType = messageWithMetadata.metadata?.type as string

    // Ensure message ordering by adding sequence number
    const sequencedMessage = {
      ...message,
      metadata: {
        ...messageWithMetadata.metadata,
        sequence: messageSequenceRef.current++,
        threadId: taskId
      }
    }

    // Handle different message types
    switch (messageType) {
      case 'task_start': {
        // Initialize message thread for this task
        messageThreadRef.current.set(taskId, [sequencedMessage])

        // Update task state
        const taskInfo: TaskInfo = {
          id: taskId || '',
          sandbox_id: sandboxId,
          command,
          status: 'running',
          created_at: (messageWithMetadata.metadata?.timestamp as string) || new Date().toISOString(),
          started_at: messageWithMetadata.metadata?.timestamp as string
        }

        // Track task start time for metrics
        taskStartTimesRef.current.set(taskId, Date.now())

        // Update task history and metrics
        updateTaskHistory(taskInfo)
        setTaskMetrics(prev => ({ ...prev, totalTasks: prev.totalTasks + 1 }))

        // Set task timeout for automatic cancellation
        setTaskTimeout(taskId || '')

        setStreamingState(prev => ({
          ...prev,
          connectionState: 'connected',
          activeTask: taskInfo
        }))

        // Add start message to UI
        setMessages(prev => [...prev, sequencedMessage])
        options.onTaskStart?.(taskInfo)
        break
      }

      case 'task_log': {
        // Add log message to thread
        const existingThread = messageThreadRef.current.get(taskId) || []
        messageThreadRef.current.set(taskId, [...existingThread, sequencedMessage])

        // Add log message to UI (maintain ordering)
        setMessages(prev => {
          // Find insertion point to maintain order
          const newMessages = [...prev, sequencedMessage]
          return newMessages.sort((a, b) => {
            const msgA = a as Message & { metadata?: Record<string, unknown> }
            const msgB = b as Message & { metadata?: Record<string, unknown> }
            const seqA = msgA.metadata?.sequence as number || 0
            const seqB = msgB.metadata?.sequence as number || 0
            return seqA - seqB
          })
        })
        break
      }

      case 'task_complete': {
        // Get all log messages for this task to merge into final result
        const taskThread = messageThreadRef.current.get(taskId) || []
        const logMessages = taskThread.filter(msg => {
          const msgMetadata = (msg as any).metadata
          const content = msg.content
          
          // Only include actual command output, filter out system messages
          return msgMetadata?.type === 'task_log' && isActualCommandOutput(content)
        })

        // Merge all log content into a single output
        const mergedOutput = logMessages.map(msg => msg.content).join('')

        // Calculate execution time if not provided
        const startTime = taskStartTimesRef.current.get(taskId)
        const executionTime = messageWithMetadata.metadata?.execution_time as number ||
          (startTime ? (Date.now() - startTime) / 1000 : 0)

        const exitCode = messageWithMetadata.metadata?.exit_code as number || 0

        // Update task state
        const completedTaskInfo: TaskInfo = {
          id: taskId || '',
          sandbox_id: sandboxId,
          command,
          status: 'completed',
          created_at: streamingState.activeTask?.created_at || new Date().toISOString(),
          started_at: streamingState.activeTask?.started_at,
          completed_at: (messageWithMetadata.metadata?.timestamp as string) || new Date().toISOString(),
          exit_code: exitCode,
          execution_time: executionTime
        }

        // Update task history and clear timeout
        updateTaskHistory(completedTaskInfo)
        const timeout = taskTimeoutRef.current.get(taskId)
        if (timeout) {
          clearTimeout(timeout)
          taskTimeoutRef.current.delete(taskId)
        }
        taskStartTimesRef.current.delete(taskId)

        setStreamingState(prev => ({
          ...prev,
          activeTask: completedTaskInfo
        }))

        // Create result message with merged content and persist it
        const resultMessage = adapter.createCommandResultMessage(
          sandboxId,
          command,
          mergedOutput,
          exitCode,
          executionTime
        )

        // Persist the merged result to backend for reload functionality
        try {
          await adapter.sendMessageWithMetadata(
            sandboxId,
            mergedOutput,
            'system',
            {
              originalCommand: command,
              exitCode,
              executionTime,
              summary: `Command ${exitCode === 0 ? 'completed successfully' : 'failed'} in ${executionTime.toFixed(2)}s`,
              artifacts: adapter.detectArtifacts(mergedOutput),
              task_id: taskId
            }
          )
        } catch (error) {
          console.error('Failed to persist task result:', error)
        }

        // Remove individual log messages from UI and replace with final result
        setMessages(prev => {
          // Filter out all task-related messages for this task
          const filteredMessages = prev.filter(msg => {
            const msgMetadata = (msg as any).metadata
            return !(msgMetadata?.task_id === taskId && 
                    ['task_start', 'task_log'].includes(msgMetadata?.type))
          })
          
          // Add the final result message
          return [...filteredMessages, resultMessage]
        })

        // Clean up message thread
        messageThreadRef.current.delete(taskId)

        // Clear streaming state since task is completed
        setStreamingState(prev => ({
          ...prev,
          isStreaming: false,
          connectionState: 'disconnected',
          activeTask: null
        }))
        setIsExecutingCommand(false)

        // Disconnect connection manager
        if (connectionManagerRef.current) {
          connectionManagerRef.current.disconnect()
          connectionManagerRef.current = null
        }

        // Clear streaming control
        if (streamingControlRef.current) {
          streamingControlRef.current.cancel()
          streamingControlRef.current = null
        }

        options.onTaskComplete?.(completedTaskInfo)
        break
      }

      case 'task_error': {
        // Add error message to thread
        const errorThread = messageThreadRef.current.get(taskId) || []
        messageThreadRef.current.set(taskId, [...errorThread, sequencedMessage])

        // Calculate execution time for failed task
        const startTime = taskStartTimesRef.current.get(taskId)
        const executionTime = startTime ? (Date.now() - startTime) / 1000 : 0

        // Update task state as failed
        const failedTaskInfo: TaskInfo = {
          id: taskId || '',
          sandbox_id: sandboxId,
          command,
          status: 'failed',
          created_at: streamingState.activeTask?.created_at || new Date().toISOString(),
          started_at: streamingState.activeTask?.started_at,
          completed_at: (messageWithMetadata.metadata?.timestamp as string) || new Date().toISOString(),
          exit_code: messageWithMetadata.metadata?.exit_code as number || 1,
          execution_time: executionTime
        }

        // Update task history and clear timeout
        updateTaskHistory(failedTaskInfo)
        const timeout = taskTimeoutRef.current.get(taskId)
        if (timeout) {
          clearTimeout(timeout)
          taskTimeoutRef.current.delete(taskId)
        }
        taskStartTimesRef.current.delete(taskId)

        // Create task error
        const taskError: TaskError = {
          code: 'TASK_EXECUTION_ERROR',
          message: message.content,
          task_id: taskId || '',
          recoverable: false,
          timestamp: messageWithMetadata.metadata?.timestamp as string
        }

        setStreamingState(prev => ({
          ...prev,
          isStreaming: false,
          connectionState: 'disconnected',
          lastError: taskError,
          activeTask: null
        }))

        setIsExecutingCommand(false)

        // Disconnect connection manager
        if (connectionManagerRef.current) {
          connectionManagerRef.current.disconnect()
          connectionManagerRef.current = null
        }

        // Clear streaming control
        if (streamingControlRef.current) {
          streamingControlRef.current.cancel()
          streamingControlRef.current = null
        }

        // Add error message to UI
        setMessages(prev => [...prev, sequencedMessage])
        options.onTaskError?.(taskError)
        break
      }

      default: {
        // Handle unknown message types
        setMessages(prev => [...prev, sequencedMessage])
        break
      }
    }
  // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [sandboxId, streamingState.activeTask, options])

  // Use AI SDK's useChat hook with custom configuration
  const {
    messages,
    input,
    setInput,
    isLoading,
    setMessages
  } = useChat({
    initialMessages: options.initialMessages || [],
    onError: (error) => {
      setError(error.message)
      options.onError?.(error)
    },
    // We'll handle API calls manually through FastAPI adapter
    api: '/api/chat/dummy', // Dummy endpoint, won't be used
    onFinish: () => {
      // This won't be called since we handle everything manually
    }
  })

  /**
   * Load initial chat history
   */
  const loadHistory = useCallback(async (forceReload = false) => {
    if (!forceReload && (loadingRef.current || historyLoaded)) return
    
    loadingRef.current = true
    setIsLoadingHistory(true)
    setError(null)

    try {
      const response = await adapter.loadHistory(sandboxId, 1)
      
      setMessages(response.messages)
      setPagination({
        page: response.pagination.page,
        total: response.pagination.total,
        hasNextPage: response.pagination.hasNextPage
      })
      
      setHistoryLoaded(true)
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to load history'
      setError(errorMessage)
      console.error('Failed to load chat history:', err)
    } finally {
      setIsLoadingHistory(false)
      loadingRef.current = false
    }
  }, [sandboxId, adapter, setMessages, historyLoaded])

  /**
   * Load more history for pagination
   */
  const loadMoreHistory = useCallback(async () => {
    if (!pagination.hasNextPage || isLoadingHistory) return

    setIsLoadingHistory(true)
    try {
      const response = await adapter.loadHistory(sandboxId, pagination.page + 1)
      
      // Prepend older messages
      setMessages(prevMessages => [...response.messages, ...prevMessages])
      setPagination({
        page: response.pagination.page,
        total: response.pagination.total,
        hasNextPage: response.pagination.hasNextPage
      })
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to load more history'
      setError(errorMessage)
    } finally {
      setIsLoadingHistory(false)
    }
  }, [sandboxId, adapter, pagination, isLoadingHistory, setMessages])

  /**
   * Execute command with streaming support
   */
  const executeCommandWithStreaming = useCallback(async (command: string) => {
    console.log('🚀 executeCommandWithStreaming called:', { command, isStreamingEnabled })
    
    if (!isStreamingEnabled) {
      console.log('⚠️ Streaming disabled, falling back to regular execution')
      // Fall back to regular execution
      return executeCommandRegular(command)
    }

    console.log('✅ Starting streaming execution for command:', command)
    setIsExecutingCommand(true)
    setError(null)

    // Update streaming state
    setStreamingState(prev => ({
      ...prev,
      isStreaming: true,
      connectionState: 'connecting',
      activeTask: {
        id: `temp-${Date.now()}`,
        sandbox_id: sandboxId,
        command,
        status: 'pending',
        created_at: new Date().toISOString()
      }
    }))

    try {
      console.log('🔗 Creating connection manager for streaming')
      // Create and configure connection manager
      const connectionManager = createConnectionManager(command)
      connectionManagerRef.current = connectionManager

      // Set up streaming control interface for compatibility
      streamingControlRef.current = {
        cancel: () => {
          connectionManager.disconnect()
          connectionManagerRef.current = null
        }
      }

      console.log('📡 Starting streaming connection...')
      // Start the connection
      await connectionManager.connect()

      console.log('✅ Streaming connection established successfully')
      options.onCommandExecute?.(command, sandboxId)
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Streaming command execution failed'
      console.error('❌ Streaming execution failed:', err)
      console.log('🔄 Falling back to regular execution')
      setError(errorMessage)

      setStreamingState(prev => ({
        ...prev,
        isStreaming: false,
        connectionState: 'error',
        activeTask: null,
        lastError: {
          code: 'STREAMING_ERROR',
          message: errorMessage,
          recoverable: true
        }
      }))

      setIsExecutingCommand(false)
      streamingControlRef.current = null

      // Fall back to regular execution
      await executeCommandRegular(command)
      
      // Ensure streaming state is cleared after fallback
      setStreamingState(prev => ({
        ...prev,
        isStreaming: false,
        connectionState: 'disconnected',
        activeTask: null
      }))
    }
  // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [sandboxId, adapter, isStreamingEnabled, options, streamingState.activeTask, processStreamingMessage])

  /**
   * Execute command in sandbox (regular non-streaming)
   */
  const executeCommandRegular = useCallback(async (command: string) => {
    setIsExecutingCommand(true)
    setError(null)

    try {
      const response = await adapter.executeCommand(sandboxId, command)
      const { content, isError } = adapter.formatCommandOutput(response)
      
      
      // Send the command result to backend for persistence
      const resultContent = content || '(No output)'
      const messageType = isError ? 'error' : 'system'
      
      try {
        // Create enhanced metadata for persistence
        const enhancedMetadata = {
          originalCommand: command,
          exitCode: response.exit_code,
          executionTime: response.execution_time,
          summary: `Command ${response.exit_code === 0 ? 'completed successfully' : 'failed'} in ${response.execution_time.toFixed(2)}s`,
          artifacts: adapter.detectArtifacts(resultContent),
          hasOutput: resultContent && resultContent.trim().length > 0
        }
        
        await adapter.sendMessageWithMetadata(
          sandboxId,
          resultContent,
          messageType as 'user' | 'system' | 'error' | 'status',
          enhancedMetadata
        )
        
        // Add the command result with enhanced metadata
        const resultMessage = isError 
          ? adapter.createErrorMessage(sandboxId, resultContent, enhancedMetadata)
          : adapter.createCommandResultMessage(
              sandboxId,
              command,
              resultContent,
              response.exit_code,
              response.execution_time
            )

        setMessages(prev => [...prev, resultMessage])
      } catch (persistError) {
        console.error('Failed to persist command result:', persistError)
        // Still show the result locally even if persistence fails
        const enhancedMetadata = {
          originalCommand: command,
          exitCode: response.exit_code,
          executionTime: response.execution_time,
          summary: `Command ${response.exit_code === 0 ? 'completed successfully' : 'failed'} in ${response.execution_time.toFixed(2)}s`,
          artifacts: adapter.detectArtifacts(resultContent),
          hasOutput: resultContent && resultContent.trim().length > 0
        }
        
        const resultMessage = isError 
          ? adapter.createErrorMessage(sandboxId, resultContent, enhancedMetadata)
          : adapter.createCommandResultMessage(
              sandboxId,
              command,
              resultContent,
              response.exit_code,
              response.execution_time
            )

        setMessages(prev => [...prev, resultMessage])
      }
      
      options.onCommandExecute?.(command, sandboxId)
      
      // Clear any residual streaming state after successful regular execution
      setStreamingState(prev => ({
        ...prev,
        isStreaming: false,
        connectionState: 'disconnected',
        activeTask: null
      }))
    } catch (err) {
      
      const errorMessage = err instanceof Error ? err.message : 'Command execution failed'
      
      try {
        // Persist error to backend with metadata
        const errorMetadata = { originalCommand: command }
        
        await adapter.sendMessageWithMetadata(sandboxId, errorMessage, 'error', errorMetadata)
        
        // Add error message directly to avoid excessive API calls
        const errorMsg = adapter.createErrorMessage(sandboxId, errorMessage, errorMetadata)
        setMessages(prev => [...prev, errorMsg])
      } catch (persistError) {
        console.error('Failed to persist error message:', persistError)
        
        // Fallback: show local error message
        const errorMsg = adapter.createErrorMessage(sandboxId, errorMessage, {
          originalCommand: command
        })
        
        setMessages(prev => [...prev, errorMsg])
      }
      setError(errorMessage)
    } finally {
      setIsExecutingCommand(false)
    }
  }, [sandboxId, adapter, setMessages, options])

  /**
   * Get messages for a specific task thread
   */
  const getTaskMessages = useCallback((taskId: string): Message[] => {
    return messageThreadRef.current.get(taskId) || []
  }, [])

  /**
   * Get all active task threads
   */
  const getActiveThreads = useCallback((): Map<string, Message[]> => {
    return new Map(messageThreadRef.current)
  }, [])

  /**
   * Add or update task in history
   */
  const updateTaskHistory = useCallback((taskInfo: TaskInfo) => {
    setTaskHistory(prev => {
      const newHistory = new Map(prev)
      newHistory.set(taskInfo.id, taskInfo)
      return newHistory
    })

    // Update metrics
    if (taskInfo.status === 'completed' || taskInfo.status === 'failed') {
      setTaskMetrics(prev => {
        const newMetrics = { ...prev }

        if (taskInfo.status === 'completed') {
          newMetrics.completedTasks += 1

          // Update average execution time
          if (taskInfo.execution_time) {
            const totalTime = prev.averageExecutionTime * prev.completedTasks + taskInfo.execution_time
            newMetrics.averageExecutionTime = totalTime / newMetrics.completedTasks
          }
        } else if (taskInfo.status === 'failed') {
          newMetrics.failedTasks += 1
        }

        return newMetrics
      })
    }
  }, [])

  /**
   * Get task by ID from history
   */
  const getTaskById = useCallback((taskId: string): TaskInfo | undefined => {
    return taskHistory.get(taskId)
  }, [taskHistory])

  /**
   * Get all tasks with specific status
   */
  const getTasksByStatus = useCallback((status: TaskInfo['status']): TaskInfo[] => {
    return Array.from(taskHistory.values()).filter(task => task.status === status)
  }, [taskHistory])

  /**
   * Get recent tasks (last N tasks)
   */
  const getRecentTasks = useCallback((limit = 10): TaskInfo[] => {
    return Array.from(taskHistory.values())
      .sort((a, b) => new Date(b.created_at).getTime() - new Date(a.created_at).getTime())
      .slice(0, limit)
  }, [taskHistory])

  /**
   * Clear task history
   */
  const clearTaskHistory = useCallback(() => {
    setTaskHistory(new Map())
    setTaskMetrics({
      totalTasks: 0,
      completedTasks: 0,
      failedTasks: 0,
      averageExecutionTime: 0
    })

    // Clear all timeouts
    taskTimeoutRef.current.forEach(timeout => clearTimeout(timeout))
    taskTimeoutRef.current.clear()
    taskStartTimesRef.current.clear()
  }, [])

  /**
   * Set task timeout for automatic cancellation
   */
  const setTaskTimeout = useCallback((taskId: string, timeoutMs = 300000) => { // 5 minutes default
    // Clear existing timeout
    const existingTimeout = taskTimeoutRef.current.get(taskId)
    if (existingTimeout) {
      clearTimeout(existingTimeout)
    }

    // Set new timeout
    const timeout = setTimeout(() => {
      const task = taskHistory.get(taskId)
      if (task && (task.status === 'pending' || task.status === 'running')) {
        // Auto-cancel timed out task
        const timedOutTask: TaskInfo = {
          ...task,
          status: 'failed',
          completed_at: new Date().toISOString(),
          execution_time: (Date.now() - taskStartTimesRef.current.get(taskId)!) / 1000
        }

        updateTaskHistory(timedOutTask)

        // Cancel streaming if this is the active task
        if (streamingState.activeTask?.id === taskId) {
          cancelTask()
        }

        // Notify error callback
        options.onTaskError?.({
          code: 'TASK_TIMEOUT',
          message: `Task ${taskId} timed out after ${timeoutMs / 1000} seconds`,
          task_id: taskId,
          recoverable: false,
          timestamp: new Date().toISOString()
        })
      }

      taskTimeoutRef.current.delete(taskId)
      taskStartTimesRef.current.delete(taskId)
    }, timeoutMs)

    taskTimeoutRef.current.set(taskId, timeout)
  // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [taskHistory, streamingState.activeTask?.id, updateTaskHistory, options])

  /**
   * Create and configure connection manager
   */
  const createConnectionManager = useCallback((command: string) => {
    const url = buildStreamingUrl(sandboxId)
    const authToken = getAuthToken()
    console.log('🔧 Creating StreamingConnectionManager:', { url, command, hasAuthToken: !!authToken })

    const manager = new StreamingConnectionManager(
      url,
      command,
      {
        ...DEFAULT_STREAMING_OPTIONS,
        maxReconnectAttempts: 3,
        reconnectDelay: 1000,
        connectionTimeout: 30000
      },
      {
        onMessage: (message) => {
          console.log('📨 Received streaming message:', message)
          const aiMessage = FastAPIAdapter.convertSSEToAIMessage(message, sandboxId)
          processStreamingMessage(aiMessage, command).catch(error => {
            console.error('Error processing streaming message:', error)
          })
        },
        onError: (error) => {
          console.error('🚨 Streaming connection error:', error)
          setError(error.message)
          setStreamingState(prev => ({
            ...prev,
            connectionState: 'error',
            lastError: error
          }))

          // Update connection metrics
          setConnectionMetrics(prev => ({
            ...prev,
            failedConnections: prev.failedConnections + 1,
            reconnectAttempts: prev.reconnectAttempts + (error.recoverable ? 1 : 0)
          }))

          options.onTaskError?.(error)
        },
        onConnectionChange: (state) => {
          console.log('🔄 Streaming connection state changed:', state)
          setStreamingState(prev => ({
            ...prev,
            connectionState: state,
            reconnectAttempts: connectionManagerRef.current?.getReconnectAttempts() || 0
          }))

          // Update connection metrics
          if (state === 'connected') {
            setConnectionMetrics(prev => ({
              ...prev,
              totalConnections: prev.totalConnections + 1,
              lastConnectionTime: new Date()
            }))
          }
        }
      },
      authToken || undefined
    )

    return manager
  }, [sandboxId, processStreamingMessage, options])

  /**
   * Force reconnect streaming connection
   */
  const reconnectStreaming = useCallback(async () => {
    if (connectionManagerRef.current) {
      try {
        await connectionManagerRef.current.reconnect()
      } catch (error) {
        const taskError = {
          code: 'RECONNECT_FAILED',
          message: error instanceof Error ? error.message : 'Failed to reconnect',
          recoverable: true,
          timestamp: new Date().toISOString()
        }

        setStreamingState(prev => ({
          ...prev,
          connectionState: 'error',
          lastError: taskError
        }))

        options.onTaskError?.(taskError)
      }
    }
  }, [options])

  /**
   * Get connection health status
   */
  const getConnectionHealth = useCallback(() => {
    const manager = connectionManagerRef.current
    if (!manager) {
      return {
        isHealthy: false,
        state: 'disconnected' as const,
        reconnectAttempts: 0,
        canReconnect: false
      }
    }

    const state = manager.getConnectionState()
    const reconnectAttempts = manager.getReconnectAttempts()

    return {
      isHealthy: state === 'connected',
      state,
      reconnectAttempts,
      canReconnect: reconnectAttempts < DEFAULT_STREAMING_OPTIONS.maxReconnectAttempts
    }
  }, [])

  /**
   * Reset connection metrics
   */
  const resetConnectionMetrics = useCallback(() => {
    setConnectionMetrics({
      totalConnections: 0,
      failedConnections: 0,
      reconnectAttempts: 0,
      lastConnectionTime: null,
      averageConnectionTime: 0
    })
  }, [])

  /**
   * Cancel active streaming task
   */
  const cancelTask = useCallback(() => {
    const activeTaskId = streamingState.activeTask?.id

    // Disconnect connection manager
    if (connectionManagerRef.current) {
      connectionManagerRef.current.disconnect()
      connectionManagerRef.current = null
    }

    // Cancel streaming control
    if (streamingControlRef.current) {
      streamingControlRef.current.cancel()
      streamingControlRef.current = null
    }

    // Update task state to cancelled if there's an active task
    if (activeTaskId && streamingState.activeTask) {
      const startTime = taskStartTimesRef.current.get(activeTaskId)
      const executionTime = startTime ? (Date.now() - startTime) / 1000 : 0

      const cancelledTask: TaskInfo = {
        ...streamingState.activeTask,
        status: 'cancelled',
        completed_at: new Date().toISOString(),
        execution_time: executionTime
      }

      updateTaskHistory(cancelledTask)

      // Clear timeout and start time
      const timeout = taskTimeoutRef.current.get(activeTaskId)
      if (timeout) {
        clearTimeout(timeout)
        taskTimeoutRef.current.delete(activeTaskId)
      }
      taskStartTimesRef.current.delete(activeTaskId)

      // Clean up current task thread
      messageThreadRef.current.delete(activeTaskId)
    }

    setStreamingState(prev => ({
      ...prev,
      isStreaming: false,
      connectionState: 'disconnected',
      activeTask: null
    }))

    setIsExecutingCommand(false)
  }, [streamingState.activeTask, updateTaskHistory])

  /**
   * Execute command (with streaming support)
   */
  const executeCommand = useCallback(async (command: string) => {
    return executeCommandWithStreaming(command)
  }, [executeCommandWithStreaming])

  /**
   * Custom submit handler that uses FastAPI backend with streaming support
   */
  const handleSubmit = useCallback(async (e?: React.FormEvent) => {
    e?.preventDefault()

    if (!input.trim() || isLoading || isExecutingCommand) return

    const message = input.trim()
    setInput('') // Clear input immediately for better UX
    setError(null)

    // Add optimistic user message
    const userMessage = adapter.createOptimisticMessage(sandboxId, message)
    setMessages(prev => [...prev, userMessage])

    try {
      // Send message to FastAPI backend for persistence
      await adapter.sendMessage(sandboxId, message, 'user')
      
      // Check if this is a command that should be executed
      if (adapter.isExecutableCommand(message)) {
        await executeCommand(message)
      } else {
        // For non-commands, the message is already persisted and showing optimistically
        // No need to refresh history to avoid excessive API calls
      }
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to send message'
      setError(errorMessage)
      
      // Remove the optimistic message since sending failed
      setMessages(prev => prev.filter(m => m.id !== userMessage.id))
      
      // Add error message to chat and persist it
      try {
        await adapter.sendMessage(sandboxId, errorMessage, 'error')
      } catch (persistError) {
        console.error('Failed to persist error message:', persistError)
      }
      
      const errorMsg = adapter.createErrorMessage(sandboxId, errorMessage)
      setMessages(prev => [...prev, errorMsg])
    }
  }, [input, isLoading, isExecutingCommand, sandboxId, adapter, setInput, setMessages, executeCommand])

  /**
   * Clear chat history
   */
  const clearHistory = useCallback(async () => {
    try {
      await adapter.clearHistory(sandboxId)
      setMessages([])
      setPagination({
        page: 1,
        total: 0,
        hasNextPage: false
      })
      setHistoryLoaded(false)
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to clear history'
      setError(errorMessage)
    }
  }, [sandboxId, adapter, setMessages])

  /**
   * Reload current conversation
   */
  const reloadConversation = useCallback(() => {
    setHistoryLoaded(false)
    loadHistory(true) // Force reload
  }, [loadHistory])

  // Load history on mount or sandboxId change
  useEffect(() => {
    if (sandboxId) {
      setHistoryLoaded(false)
      loadHistory()
    }
  }, [sandboxId]) // Remove loadHistory from dependencies to prevent infinite loops

  // Cleanup streaming connections and message threads on unmount
  useEffect(() => {
    return () => {
      // Disconnect connection manager
      if (connectionManagerRef.current) {
        connectionManagerRef.current.disconnect()
        connectionManagerRef.current = null
      }

      if (streamingControlRef.current) {
        streamingControlRef.current.cancel()
        streamingControlRef.current = null
      }

      // Clear message threads
      messageThreadRef.current.clear()
      messageSequenceRef.current = 0

      // Clear all task timeouts
      taskTimeoutRef.current.forEach(timeout => clearTimeout(timeout))
      taskTimeoutRef.current.clear()
      taskStartTimesRef.current.clear()
    }
  }, [])

  // Cleanup completed task threads periodically
  useEffect(() => {
    const cleanupInterval = setInterval(() => {
      const now = Date.now()
      const maxAge = 5 * 60 * 1000 // 5 minutes
      const messageThreads = messageThreadRef.current

      messageThreads.forEach((thread, taskId) => {
        const lastMessage = thread[thread.length - 1]
        const messageTime = lastMessage?.createdAt?.getTime() || 0

        if (now - messageTime > maxAge) {
          messageThreads.delete(taskId)
        }
      })
    }, 60000) // Run every minute

    return () => clearInterval(cleanupInterval)
  }, [])

  return {
    messages,
    input,
    setInput,
    handleSubmit,
    isLoading: isLoading || false, // AI SDK isLoading
    isLoadingHistory,
    isSendingMessage: isLoading || isExecutingCommand, // Map AI SDK loading and command execution to our sending state
    error,
    reload: reloadConversation,
    clearHistory,
    executeCommand,
    pagination,
    loadMoreHistory,
    // New streaming properties
    activeTask: streamingState.activeTask,
    cancelTask,
    isStreaming: streamingState.isStreaming,
    streamingState,
    // Message threading utilities
    getTaskMessages,
    getActiveThreads,
    // Enhanced task management
    taskHistory,
    taskMetrics,
    getTaskById,
    getTasksByStatus,
    getRecentTasks,
    clearTaskHistory,
    // Connection management
    connectionMetrics,
    reconnectStreaming,
    getConnectionHealth,
    resetConnectionMetrics
  }
}
