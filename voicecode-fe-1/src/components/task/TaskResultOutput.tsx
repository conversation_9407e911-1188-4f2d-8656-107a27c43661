import React from 'react'
import { Badge } from '@/components/ui/badge'
import { Button } from '@/components/ui/button'
import { Copy, FileText } from 'lucide-react'
import { cn } from '@/lib/utils'

interface TaskResultOutputProps {
  output: string
  isSuccess: boolean
  artifacts?: string[]
  className?: string
}

/**
 * TaskResultOutput displays the command output and artifacts
 */
export const TaskResultOutput: React.FC<TaskResultOutputProps> = ({
  output,
  artifacts = [],
  className
}) => {
  const hasOutput = output && output.trim().length > 0
  const hasArtifacts = artifacts && artifacts.length > 0

  const handleCopyOutput = () => {
    navigator.clipboard.writeText(output)
  }

  return (
    <div className={cn("space-y-3 mt-4", className)}>
      {/* Command Output */}
      {hasOutput && (
        <div className="space-y-2">
          <div className="flex items-center justify-between">
            <span className="text-sm font-medium">Output</span>
            <Button 
              variant="ghost" 
              size="sm" 
              onClick={handleCopyOutput}
              className="h-7 px-2"
            >
              <Copy className="h-3 w-3 mr-1" />
              Copy
            </Button>
          </div>
          <pre className="text-sm bg-muted/50 p-3 rounded whitespace-pre-wrap break-words max-h-64 overflow-y-auto">
            <code>{output}</code>
          </pre>
        </div>
      )}

      {/* Artifacts */}
      {/* {hasArtifacts && (
        <div className="space-y-2">
          <div className="flex items-center gap-2">
            <FileText className="h-4 w-4" />
            <span className="text-sm font-medium">Generated Files</span>
          </div>
          <div className="flex flex-wrap gap-1">
            {artifacts.map((artifact, index) => (
              <Badge key={index} variant="outline" className="text-xs">
                {artifact}
              </Badge>
            ))}
          </div>
        </div>
      )} */}
    </div>
  )
}

export default TaskResultOutput