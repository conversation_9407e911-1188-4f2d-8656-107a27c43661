import { useEffect } from 'react'
import { Navigate, Outlet, useLocation } from 'react-router-dom'
import { useAuthStore } from '@/store/auth.store'
import { Loader2 } from 'lucide-react'

export function AuthGuard() {
  const location = useLocation()
  const { isAuthenticated, isLoading, checkAuth } = useAuthStore()

  useEffect(() => {
    console.log('🔒 AuthGuard: Checking authentication...')
    console.log('🔒 AuthGuard: isAuthenticated =', isAuthenticated)
    console.log('🔒 AuthGuard: isLoading =', isLoading)
    checkAuth()
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [checkAuth])

  if (isLoading) {
    console.log('🔒 AuthGuard: Showing loading spinner')
    return (
      <div className="min-h-screen flex items-center justify-center">
        <Loader2 className="h-8 w-8 animate-spin text-blue-500" />
      </div>
    )
  }

  if (!isAuthenticated) {
    console.log('🔒 AuthGuard: User not authenticated, redirecting to login')
    return <Navigate to="/login" state={{ from: location }} replace />
  }

  console.log('🔒 AuthGuard: User authenticated, showing protected content')
  return <Outlet />
}